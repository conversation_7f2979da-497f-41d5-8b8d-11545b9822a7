import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Collaborator, DashboardView, Document as DocType, DocumentsPaginationState, Flow, Notification, NotificationPreferences, Obligation, PricingPlan, SearchResult, Signature, SsoConfig, Team, TeamMemberRole, Template, Theme, User, WorkflowInstance, WorkflowTemplate } from '../types';
import DashboardHeader from './DashboardHeader';
import DashboardPage from './DashboardPage';
import Sidebar from './Sidebar';

// Navigation utility for path-based routing
const navigateTo = (path: string, replace: boolean = false) => {
  if (replace) {
    window.history.replaceState(null, '', path);
  } else {
    window.history.pushState(null, '', path);
  }
  // Trigger a popstate event to update the UI
  window.dispatchEvent(new PopStateEvent('popstate'));
};

// URL mapping utilities for dashboard routing
const dashboardViewToPath = (dashboardView: DashboardView): string => {
  switch (dashboardView) {
    case 'dashboard': return '/dashboard';
    case 'generate': return '/dashboard/generate';
    case 'history': return '/dashboard/history';
    case 'subscription': return '/dashboard/subscription';
    case 'settings': return '/dashboard/settings';
    case 'templates': return '/dashboard/templates';
    case 'documentDetail': return '/dashboard/document';
    case 'clientDetail': return '/dashboard/client';
    case 'lifecycle': return '/dashboard/lifecycle';
    case 'clauseLibrary': return '/dashboard/clauses';
    case 'team': return '/dashboard/team';
    case 'notifications': return '/dashboard/notifications';
    case 'analysis': return '/dashboard/analysis';
    case 'help': return '/dashboard/help';
    case 'obligations': return '/dashboard/obligations';
    case 'workflows': return '/dashboard/workflows';
    case 'clients': return '/dashboard/clients';
    case 'integrations': return '/dashboard/integrations';
    default: return '/dashboard';
  }
};

const pathToDashboardView = (path: string): DashboardView | null => {
  switch (path) {
    case '/dashboard': return 'dashboard';
    case '/dashboard/generate': return 'generate';
    case '/dashboard/history': return 'history';
    case '/dashboard/subscription': return 'subscription';
    case '/dashboard/settings': return 'settings';
    case '/dashboard/templates': return 'templates';
    case '/dashboard/document': return 'documentDetail';
    case '/dashboard/client': return 'clientDetail';
    case '/dashboard/lifecycle': return 'lifecycle';
    case '/dashboard/clauses': return 'clauseLibrary';
    case '/dashboard/team': return 'team';
    case '/dashboard/notifications': return 'notifications';
    case '/dashboard/analysis': return 'analysis';
    case '/dashboard/help': return 'help';
    case '/dashboard/obligations': return 'obligations';
    case '/dashboard/workflows': return 'workflows';
    case '/dashboard/clients': return 'clients';
    case '/dashboard/integrations': return 'integrations';
    default: return null;
  }
};

interface DashboardLayoutProps {
  user: User;
  team: Team | undefined;
  allUsers: User[]; // Pass all users for collaborator lookups
  allTeams: Team[];
  dataLoading?: boolean;
  dataError?: string | null;
  documentsPagination: DocumentsPaginationState;
  onSaveDocument: (content: string, name: string, folderId: string | null, clientId: string | null, options?: { tags?: string[] }) => void;
  onUpdateDocument: (documentId: string, newContent: string) => void;
  onRevertDocumentVersion: (documentId: string, versionId: string) => void;
  onLogout: () => void;
  onUpdateProfile: (profile: Partial<Pick<User, 'name' | 'username' | 'avatarUrl' | 'jobTitle' | 'company' | 'bio' | 'websiteUrl' | 'linkedinUrl'>>) => void;
  onDeleteAccount: () => void;
  onDeleteDocument: (documentId: string) => void;
  onCreateFolder: (name: string) => void;
  onUpdateFolder: (folderId: string, newName: string) => void;
  onDeleteFolder: (folderId: string) => void;
  onMoveDocument: (documentId: string, folderId: string | null) => void;
  onUpdateCollaborators: (documentId: string, collaborators: Collaborator[]) => void;
  onAddComment: (documentId: string, textSelection: string) => string | undefined;
  onAddReply: (documentId: string, threadId: string, content: string) => void;
  onResolveThread: (documentId: string, threadId: string) => void;
  onDeleteComment: (documentId: string, threadId: string, commentId: string) => void;
  onMarkNotificationRead: (notificationId: string) => void;
  onMarkAllNotificationsRead: () => void;
  onCreateCustomTemplate: (docId: string, templateName: string) => void;
  onDeleteCustomTemplate: (templateId: string) => void;
  onUpgradeSubscription: () => void;
  onUpdateSubscription: (teamId: string, planName: string) => void;
  onRequestSignatures: (docId: string, signers: Omit<Signature, 'id' | 'status' | 'token'>[]) => void;
  onLogDocumentView: (docId: string) => void;
  onUpdateDocumentStatus: (docId: string, newStatus: DocType['status']) => void;
  onCreateClause: (clauseData: Omit<Clause, 'id' | 'createdAt'>) => void;
  onUpdateClause: (clauseId: string, updates: Partial<Omit<Clause, 'id' | 'createdAt'>>) => void;
  onDeleteClause: (clauseId: string) => void;
  onCreateClient: (clientData: Omit<Client, 'id' | 'createdAt'>) => void;
  onUpdateClient: (clientId: string, updates: Partial<Omit<Client, 'id' | 'createdAt'>>) => void;
  onDeleteClient: (clientId: string) => void;
  onUpdateDocumentClient: (documentId: string, clientId: string | null) => void;
  onLoadMoreDocuments: () => void;
  onInviteMember: (email: string, role: TeamMemberRole) => string | null;
  onUpdateMemberRole: (userId: string, role: TeamMemberRole) => void;
  onRemoveMember: (userId: string) => void;
  onUpdateUserSettings: (settings: { theme?: Theme; notificationPreferences?: Partial<NotificationPreferences> }) => void;
  onChangePassword: (oldPassword: string, newPassword: string) => string | null;
  onGenerateApiKey: (name: string) => ApiKey;
  onRevokeApiKey: (keyId: string) => void;
  onUpdateSsoConfig: (config: SsoConfig) => void;
  pricingPlans: PricingPlan[];
  publicTemplates: Template[];
  onRequestApproval: (docId: string, approverEmails: string[]) => void;
  onRespondToApproval: (docId: string, decision: 'approved' | 'changes-requested', comments?: string) => void;
  onUpdateObligationStatus: (docId: string, obligationId: string, status: Obligation['status']) => void;
  onCreateWorkflowTemplate: (templateData: Omit<WorkflowTemplate, 'id' | 'status'>) => void;
  onUpdateWorkflowTemplate: (template: WorkflowTemplate) => void;
  onDeleteWorkflowTemplate: (templateId: string) => void;
  workflowInstances: WorkflowInstance[];
  onCreateConnection: (connectorId: string, credentials: Record<string, string>) => void;
  onDeleteConnection: (connectionId: string) => void;
  onCreateFlow: (flowData: Omit<Flow, 'id' | 'userId' | 'createdAt'>) => void;
  onUpdateFlow: (flowId: string, updates: Partial<Flow>) => void;
  onDeleteFlow: (flowId: string) => void;
  // New: allow parent to navigate into a specific dashboard view (e.g., history) and preselect a folder
  navigate?: { view: DashboardView; selectedFolderId?: string | null; anchorDocId?: string } | null;
  onConsumeNavigate?: () => void;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = (props) => {
  const {
    user,
    team,
    allUsers,
    allTeams,
    dataLoading,
    dataError,
    documentsPagination,
    onSaveDocument,
    onUpdateDocument,
    onRevertDocumentVersion,
    onLogout,
    onUpdateProfile,
    onDeleteAccount,
    onDeleteDocument,
    onCreateFolder,
    onUpdateFolder,
    onDeleteFolder,
    onMoveDocument,
    onUpdateCollaborators,
    onAddComment,
    onAddReply,
    onResolveThread,
    onDeleteComment,
    onMarkNotificationRead,
    onMarkAllNotificationsRead,
    onCreateCustomTemplate,
    onDeleteCustomTemplate,
    onUpgradeSubscription,
    onRequestSignatures,
    onUpdateSubscription,
    onLogDocumentView,
    onUpdateDocumentStatus,
    onCreateClause,
    onUpdateClause,
    onDeleteClause,
    onCreateClient,
    onUpdateClient,
    onDeleteClient,
    onUpdateDocumentClient,
    onLoadMoreDocuments,
    onInviteMember,
    onUpdateMemberRole,
    onRemoveMember,
    onUpdateUserSettings,
    onChangePassword,
    onGenerateApiKey,
    onRevokeApiKey,
    onUpdateSsoConfig,
    pricingPlans,
    publicTemplates,
    onRequestApproval,
    onRespondToApproval,
    onUpdateObligationStatus,
    onCreateWorkflowTemplate,
    onUpdateWorkflowTemplate,
    onDeleteWorkflowTemplate,
    workflowInstances,
    onCreateConnection,
    onDeleteConnection,
    onCreateFlow,
    onUpdateFlow,
    onDeleteFlow,
    navigate,
    onConsumeNavigate,
  } = props;
  const [dashboardView, setDashboardView] = useState<DashboardView>(() => {
    // Initialize dashboard view from URL path
    const path = window.location.pathname;
    const viewFromPath = pathToDashboardView(path);
    return viewFromPath || 'dashboard';
  });
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [promptToGenerate, setPromptToGenerate] = useState<string | null>(null);
  const [isTemplateSession, setIsTemplateSession] = useState<boolean>(false);
  const [activeDocument, setActiveDocument] = useState<DocType | null>(null);
  const [activeClient, setActiveClient] = useState<Client | null>(null);
  const [initialUtilityTab, setInitialUtilityTab] = useState<'suggestions' | 'history' | 'comments' | null>(null);
  const [initialHelpTopicId, setInitialHelpTopicId] = useState<string | null>(null);
  const [initialHistoryFolderId, setInitialHistoryFolderId] = useState<string | null | undefined>(undefined);
  const [highlightDocId, setHighlightDocId] = useState<string | undefined>(undefined);

  // Keep activeDocument in sync with latest user.documents
  // Only update when document is deleted or core properties change (not activity logs)
  useEffect(() => {
    if (activeDocument) {
      const fresh = user.documents.find(d => d.id === activeDocument.id);
      if (!fresh) {
        // Document was deleted - clear active document
        setActiveDocument(null);
        setDashboardView('history');
      } else if (fresh !== activeDocument) {
        // Only update if meaningful properties changed (not just activity logs)
        const meaningfulPropsChanged = (
          fresh.name !== activeDocument.name ||
          fresh.content !== activeDocument.content ||
          fresh.status !== activeDocument.status ||
          fresh.clientId !== activeDocument.clientId ||
          fresh.updatedAt !== activeDocument.updatedAt ||
          JSON.stringify(fresh.collaborators) !== JSON.stringify(activeDocument.collaborators)
        );

        if (meaningfulPropsChanged) {
          setActiveDocument(fresh);
        }
      }
    }
  }, [user.documents, activeDocument]);

  // React to navigation intents from the parent (App)
  useEffect(() => {
    if (navigate) {
      if (navigate.view && navigate.view !== dashboardView) {
        setDashboardView(navigate.view);
      }
      if (navigate.view === 'history') {
        setInitialHistoryFolderId(typeof navigate.selectedFolderId === 'undefined' ? undefined : (navigate.selectedFolderId ?? null));
        setHighlightDocId(navigate.anchorDocId);
      }
      // Consumption handled so parent can clear it
      if (onConsumeNavigate) {
        onConsumeNavigate();
      }
    }

  }, [navigate]);

  // Sync dashboard view with URL path
  useEffect(() => {
    const handleRouteChange = () => {
      const path = window.location.pathname;
      const viewFromPath = pathToDashboardView(path);
      if (viewFromPath && viewFromPath !== dashboardView) {
        setDashboardView(viewFromPath);
      }
    };

    window.addEventListener('popstate', handleRouteChange);
    return () => window.removeEventListener('popstate', handleRouteChange);
  }, [dashboardView]);

  // Update URL path when dashboard view changes
  useEffect(() => {
    const expectedPath = dashboardViewToPath(dashboardView);
    if (window.location.pathname !== expectedPath) {
      navigateTo(expectedPath, true);
    }
  }, [dashboardView]);

  // Keep activeClient in sync with user updates (reflect edits or deletion)
  useEffect(() => {
    if (activeClient) {
      const next = (user.clients || []).find(c => c.id === activeClient.id) || null;
      if (next) {
        if (next !== activeClient) { setActiveClient(next); }
      } else {
        // Client removed; go back to list
        setActiveClient(null);
        setDashboardView('clients');
      }
    }
  }, [user.clients]);

  useEffect(() => {
    if (activeDocument) {
      const updatedDocFromUser = user.documents.find(doc => doc.id === activeDocument.id);
      if (
        updatedDocFromUser && (
          updatedDocFromUser.updatedAt !== activeDocument.updatedAt ||
          updatedDocFromUser.clientId !== activeDocument.clientId ||
          updatedDocFromUser.name !== activeDocument.name
        )
      ) {
        setActiveDocument(updatedDocFromUser);
      } else if (!updatedDocFromUser) {
        setActiveDocument(null);
        setDashboardView('history');
      }
    }
  }, [user.documents, activeDocument]);

  const handleSelectTemplate = (prompt: string) => {
    setPromptToGenerate(prompt);
    setIsTemplateSession(true);
    setDashboardView('generate');
  };

  const clearPromptToGenerate = () => {
    setPromptToGenerate(null);
  };

  const handleSetView = (view: DashboardView) => {
    if (view !== 'documentDetail') {
      setActiveDocument(null);
    }
    if (view !== 'clientDetail') {
      setActiveClient(null);
    }
    if (view !== 'help') {
      setInitialHelpTopicId(null);
    }
    if (view !== 'generate') {
      setIsTemplateSession(false);
    }
    setDashboardView(view);
  };

  const handleViewDocument = (doc: DocType) => {
    onLogDocumentView(doc.id);
    setActiveDocument(doc);
    setDashboardView('documentDetail');
  };

  const handleViewClient = (client: Client) => {
    setActiveClient(client);
    setDashboardView('clientDetail');
  };

  const handleNotificationClick = (notification: Notification) => {
    onMarkNotificationRead(notification.id);
    let docToView = notification.documentId ? user.documents.find(d => d.id === notification.documentId) : undefined;

    if (!docToView && notification.documentId) {
      for (const u of allUsers) {
        docToView = u.documents.find(d => d.id === notification.documentId && d.collaborators.some(c => c.email === user.email));
        if (docToView) { break; }
      }
    }

    if (docToView) {
      if (notification.type === 'comment') {
        setInitialUtilityTab('comments');
      }
      handleViewDocument(docToView);
    } else if (notification.type === 'team') {
      handleSetView('team');
    } else {
      // Could not find document for notification - handled by UI state
    }
  };

  const handleSearchResultClick = (result: SearchResult) => {
    switch (result.type) {
      case 'document': {
        const doc = user.documents.find(d => d.id === result.id);
        if (doc) {
          handleViewDocument(doc);
        }
        break;
      }
      case 'folder':
        handleSetView('history');
        // Future enhancement: select the specific folder
        break;
      case 'clause':
        handleSetView('clauseLibrary');
        break;
      case 'help':
        setInitialHelpTopicId(result.id);
        handleSetView('help');
        break;
    }
  };

  return (
    <div className="flex h-screen bg-zinc-50 dark:bg-zinc-900">
      <Sidebar
        currentView={dashboardView}
        setView={handleSetView}
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
        user={user}
        isCollapsed={isSidebarCollapsed}
        setIsCollapsed={setIsSidebarCollapsed}
      />
      <div className="flex-1 flex flex-col overflow-hidden">
        <DashboardHeader
          onLogout={onLogout}
          user={user}
          view={dashboardView}
          onToggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)}
          onMarkAllNotificationsRead={onMarkAllNotificationsRead}
          onNotificationClick={handleNotificationClick}
          setView={handleSetView}
          onSearchResultClick={handleSearchResultClick}
          onUpdateUserSettings={(s) => onUpdateUserSettings({ theme: s.theme })}
        />
        <div className="flex-1 overflow-x-hidden overflow-y-auto bg-zinc-50 dark:bg-zinc-900">
          <DashboardPage
            user={user}
            team={team}
            allUsers={allUsers}
            allTeams={allTeams}
            dataLoading={dataLoading}
            dataError={dataError}
            documentsPagination={documentsPagination}
            onSaveDocument={onSaveDocument}
            onUpdateDocument={onUpdateDocument}
            onRevertDocumentVersion={onRevertDocumentVersion}
            view={dashboardView}
            setView={handleSetView}
            initialSelectedFolderId={initialHistoryFolderId}
            highlightDocumentId={highlightDocId}
            isTemplateSession={isTemplateSession}
            onUpdateProfile={onUpdateProfile}
            onDeleteAccount={onDeleteAccount}
            onSelectTemplate={handleSelectTemplate}
            promptToGenerate={promptToGenerate}
            onClearPrompt={clearPromptToGenerate}
            onViewDocument={handleViewDocument}
            onViewClient={handleViewClient}
            onDeleteDocument={onDeleteDocument}
            activeDocument={activeDocument}
            activeClient={activeClient}
            onCreateFolder={onCreateFolder}
            onUpdateFolder={onUpdateFolder}
            onDeleteFolder={onDeleteFolder}
            onMoveDocument={onMoveDocument}
            onUpdateCollaborators={onUpdateCollaborators}
            onAddComment={onAddComment}
            onAddReply={onAddReply}
            onResolveThread={onResolveThread}
            onDeleteComment={onDeleteComment}
            initialUtilityTab={initialUtilityTab}
            onClearInitialTab={() => setInitialUtilityTab(null)}
            onCreateCustomTemplate={onCreateCustomTemplate}
            onDeleteCustomTemplate={onDeleteCustomTemplate}
            onUpgradeSubscription={onUpgradeSubscription}
            onRequestSignatures={onRequestSignatures}
            onUpdateSubscription={onUpdateSubscription}
            onLogDocumentView={onLogDocumentView}
            onUpdateDocumentStatus={onUpdateDocumentStatus}
            onNotificationClick={handleNotificationClick}
            onCreateClause={onCreateClause}
            onUpdateClause={onUpdateClause}
            onDeleteClause={onDeleteClause}
            onCreateClient={onCreateClient}
            onUpdateClient={onUpdateClient}
            onDeleteClient={onDeleteClient}
            onUpdateDocumentClient={onUpdateDocumentClient}
            onLoadMoreDocuments={onLoadMoreDocuments}
            onInviteMember={onInviteMember}
            onUpdateMemberRole={onUpdateMemberRole}
            onRemoveMember={onRemoveMember}
            onMarkAllNotificationsRead={onMarkAllNotificationsRead}
            onUpdateUserSettings={onUpdateUserSettings}
            onChangePassword={onChangePassword}
            onGenerateApiKey={onGenerateApiKey}
            onRevokeApiKey={onRevokeApiKey}
            onUpdateSsoConfig={onUpdateSsoConfig}
            initialHelpTopicId={initialHelpTopicId}
            pricingPlans={pricingPlans}
            publicTemplates={publicTemplates}
            onRequestApproval={onRequestApproval}
            onRespondToApproval={onRespondToApproval}
            onUpdateObligationStatus={onUpdateObligationStatus}
            onCreateWorkflowTemplate={onCreateWorkflowTemplate}
            onUpdateWorkflowTemplate={onUpdateWorkflowTemplate}
            onDeleteWorkflowTemplate={onDeleteWorkflowTemplate}
            workflowInstances={workflowInstances}
            onCreateConnection={onCreateConnection}
            onDeleteConnection={onDeleteConnection}
            onCreateFlow={onCreateFlow}
            onUpdateFlow={onUpdateFlow}
            onDeleteFlow={onDeleteFlow}
          />
          <footer className="text-center py-4 px-8 border-t border-zinc-200 dark:border-zinc-800 mt-8">
            <p className="text-sm text-zinc-500 dark:text-zinc-400">
              &copy; {new Date().getFullYear()} LexiGen. All rights reserved. |
              <a href="#terms" className="ml-2 text-brand-600 hover:underline">Terms & Conditions</a>
            </p>
          </footer>
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
