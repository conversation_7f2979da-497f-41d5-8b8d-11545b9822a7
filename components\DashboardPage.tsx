import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Collaborator, DashboardView, Document as DocType, DocumentsPaginationState, Flow, Notification, NotificationPreferences, Obligation, PricingPlan, Signature, SsoConfig, Team, TeamMemberRole, Template, Theme, User, WorkflowInstance, WorkflowTemplate } from '../types';
import AnalyzePage from './AnalyzePage';
import ChatInterface from './ChatInterface';
import ClauseLibraryPage from './ClauseLibraryPage';
import ClientDetailPage from './ClientDetailPage';
import ClientsPage from './ClientsPage';
import DashboardHome from './DashboardHome';
import DocumentDetailPage from './DocumentDetailPage';
import DocumentHistoryTable from './DocumentHistoryTable';
import HelpCenterPage from './HelpCenterPage';
import IntegrationsPage from './IntegrationsPage';
import LifecyclePage from './LifecyclePage';
import NotificationsPage from './NotificationsPage';
import ObligationsPage from './ObligationsPage';
import SettingsPage from './SettingsPage';
import SubscriptionPage from './SubscriptionPage';
import TeamManagementPage from './TeamManagementPage';
import TemplatesPage from './TemplatesPage';
import WorkflowBuilderPage from './WorkflowBuilderPage';

interface DashboardPageProps {
  user: User;
  team: Team | undefined;
  allUsers: User[];
  allTeams: Team[];
  dataLoading?: boolean;
  dataError?: string | null;
  documentsPagination: DocumentsPaginationState;
  onSaveDocument: (content: string, name: string, folderId: string | null, clientId: string | null, options?: { tags?: string[] }) => void;
  onUpdateDocument: (documentId: string, newContent: string) => void;
  onRevertDocumentVersion: (documentId: string, versionId: string) => void;
  view: DashboardView;
  setView: (view: DashboardView) => void;
  // New: allow parent to suggest which folder to show in history
  initialSelectedFolderId?: string | 'all' | 'uncategorized' | 'archived' | null;
  // New: allow highlighting a specific document row
  highlightDocumentId?: string;
  onUpdateProfile: (profile: Partial<Pick<User, 'name' | 'username' | 'avatarUrl' | 'jobTitle' | 'company' | 'bio' | 'websiteUrl' | 'linkedinUrl'>>) => void;
  onDeleteAccount: () => void;
  onSelectTemplate: (prompt: string) => void;
  promptToGenerate: string | null;
  onClearPrompt: () => void;
  isTemplateSession?: boolean;
  onViewDocument: (doc: DocType) => void;
  onViewClient: (client: Client) => void;
  onDeleteDocument: (documentId: string) => void;
  activeDocument: DocType | null;
  activeClient?: Client | null;
  onCreateFolder: (name: string) => void;
  onUpdateFolder: (folderId: string, newName: string) => void;
  onDeleteFolder: (folderId: string) => void;
  onMoveDocument: (documentId: string, folderId: string | null) => void;
  onUpdateCollaborators: (documentId: string, collaborators: Collaborator[]) => void;
  onAddComment: (documentId: string, textSelection: string) => string | undefined;
  onAddReply: (documentId: string, threadId: string, content: string) => void;
  onResolveThread: (documentId: string, threadId: string) => void;
  onDeleteComment: (documentId: string, threadId: string, commentId: string) => void;
  initialUtilityTab: 'suggestions' | 'history' | 'comments' | null;
  onClearInitialTab: () => void;
  onCreateCustomTemplate: (docId: string, templateName: string) => void;
  onDeleteCustomTemplate: (templateId: string) => void;
  onUpgradeSubscription: () => void;
  onRequestSignatures: (docId: string, signers: Omit<Signature, 'id' | 'status' | 'token'>[]) => void;
  onUpdateSubscription: (teamId: string, planName: string) => void;
  onLogDocumentView: (docId: string) => void;
  onUpdateDocumentStatus: (docId: string, newStatus: DocType['status']) => void;
  onNotificationClick: (notification: Notification) => void;
  onCreateClause: (clauseData: Omit<Clause, 'id' | 'createdAt'>) => void;
  onUpdateClause: (clauseId: string, updates: Partial<Omit<Clause, 'id' | 'createdAt'>>) => void;
  onDeleteClause: (clauseId: string) => void;
  onCreateClient: (clientData: Omit<Client, 'id' | 'createdAt'>) => void;
  onUpdateClient: (clientId: string, updates: Partial<Omit<Client, 'id' | 'createdAt'>>) => void;
  onDeleteClient: (clientId: string) => void;
  onUpdateDocumentClient: (documentId: string, clientId: string | null) => void;
  onLoadMoreDocuments: () => void;
  onInviteMember: (email: string, role: TeamMemberRole) => string | null;
  onUpdateMemberRole: (userId: string, role: TeamMemberRole) => void;
  onRemoveMember: (userId: string) => void;
  onMarkAllNotificationsRead: () => void;
  onUpdateUserSettings: (settings: { theme?: Theme; notificationPreferences?: Partial<NotificationPreferences> }) => void;
  onChangePassword: (oldPassword: string, newPassword: string) => string | null;
  onGenerateApiKey: (name: string) => ApiKey;
  onRevokeApiKey: (keyId: string) => void;
  onUpdateSsoConfig: (config: SsoConfig) => void;
  initialHelpTopicId: string | null;
  pricingPlans: PricingPlan[];
  publicTemplates: Template[];
  onRequestApproval: (docId: string, approverEmails: string[]) => void;
  onRespondToApproval: (docId: string, decision: 'approved' | 'changes-requested', comments?: string) => void;
  onUpdateObligationStatus: (docId: string, obligationId: string, status: Obligation['status']) => void;
  onCreateWorkflowTemplate: (templateData: Omit<WorkflowTemplate, 'id' | 'status'>) => void;
  onUpdateWorkflowTemplate: (template: WorkflowTemplate) => void;
  onDeleteWorkflowTemplate: (templateId: string) => void;
  workflowInstances: WorkflowInstance[];
  onCreateConnection: (connectorId: string, credentials: Record<string, string>) => void;
  onDeleteConnection: (connectionId: string) => void;
  onCreateFlow: (flowData: Omit<Flow, 'id' | 'userId' | 'createdAt'>) => void;
  onUpdateFlow: (flowId: string, updates: Partial<Flow>) => void;
  onDeleteFlow: (flowId: string) => void;
}

const DashboardPage: React.FC<DashboardPageProps> = (props) => {
  const {
    user,
    team,
    allUsers,
    allTeams,
    dataLoading,
    dataError,
    documentsPagination,
    onSaveDocument,
    onUpdateDocument,
    onRevertDocumentVersion,
    view,
    setView,
    initialSelectedFolderId,
    onUpdateProfile,
    onDeleteAccount,
    onSelectTemplate,
    promptToGenerate,
    onClearPrompt,
    isTemplateSession,
    highlightDocumentId,
    onViewDocument,
    onViewClient,
    onDeleteDocument,
    activeDocument,
    activeClient,
    onCreateFolder,
    onUpdateFolder,
    onDeleteFolder,
    onMoveDocument,
    onUpdateCollaborators,
    onAddComment,
    onAddReply,
    onResolveThread,
    onDeleteComment,
    initialUtilityTab,
    onClearInitialTab,
    onCreateCustomTemplate,
    onDeleteCustomTemplate,
    onUpgradeSubscription: _onUpgradeSubscription,
    onRequestSignatures,
    onUpdateSubscription,
    onUpdateDocumentStatus,
    onLogDocumentView: _onLogDocumentView,
    onNotificationClick,
    onCreateClause,
    onUpdateClause,
    onDeleteClause,
    onCreateClient,
    onUpdateClient,
    onDeleteClient,
    onUpdateDocumentClient,
    onLoadMoreDocuments,
    onInviteMember,
    onUpdateMemberRole,
    onRemoveMember,
    onMarkAllNotificationsRead,
    onUpdateUserSettings,
    onChangePassword,
    onGenerateApiKey,
    onRevokeApiKey,
    onUpdateSsoConfig,
    initialHelpTopicId,
    pricingPlans,
    publicTemplates,
    onRequestApproval,
    onRespondToApproval,
    onUpdateObligationStatus,
    onCreateWorkflowTemplate,
    onUpdateWorkflowTemplate,
    onDeleteWorkflowTemplate,
    workflowInstances,
    onCreateConnection,
    onDeleteConnection,
    onCreateFlow,
    onUpdateFlow,
    onDeleteFlow,
  } = props;

  const renderViewContent = () => {
    switch (view) {
      case 'dashboard':
        return <DashboardHome user={user} setView={setView} onViewDocument={onViewDocument} publicTemplates={publicTemplates} />;
      case 'generate':
        return (
          <div className="p-4 sm:p-6 lg:p-8">
            <ChatInterface
              user={user}
              onSaveDocument={onSaveDocument}
              initialPrompt={promptToGenerate}
              onInitialPromptHandled={onClearPrompt}
              fromTemplate={!!isTemplateSession}
            />
          </div>
        );
      case 'history':
        return (
          <div className="p-4 sm:p-6 lg:p-8 h-full">
            <DocumentHistoryTable
              user={user}
              onView={onViewDocument}
              onDeleteDocument={onDeleteDocument}
              onCreateFolder={onCreateFolder}
              onUpdateFolder={onUpdateFolder}
              onDeleteFolder={onDeleteFolder}
              onMoveDocument={onMoveDocument}
              onUpdateDocumentStatus={onUpdateDocumentStatus}
              setView={setView}
              documentsPagination={documentsPagination}
              onLoadMoreDocuments={onLoadMoreDocuments}
              initialSelectedFolderId={initialSelectedFolderId}
              highlightDocumentId={highlightDocumentId}
            />
          </div>
        );
      case 'clients':
        return <ClientsPage
          user={user}
          onCreateClient={onCreateClient}
          onUpdateClient={onUpdateClient}
          onDeleteClient={onDeleteClient}
          onViewClient={(c) => onViewClient(c)}
        />;
      case 'clientDetail':
        if (activeClient) {
          return <ClientDetailPage
            user={user}
            client={activeClient}
            onBack={() => setView('clients')}
            onUpdateClient={onUpdateClient}
            onDeleteClient={onDeleteClient}
            onViewDocument={onViewDocument}
          />;
        }
        setView('clients');
        return null;
      case 'templates':
        return <TemplatesPage
          user={user}
          publicTemplates={publicTemplates}
          onSelectTemplate={onSelectTemplate}
          setView={setView}
          onDeleteCustomTemplate={onDeleteCustomTemplate}
        />;
      case 'clauseLibrary':
        return <ClauseLibraryPage
          user={user}
          setView={setView}
          onCreateClause={onCreateClause}
          onUpdateClause={onUpdateClause}
          onDeleteClause={onDeleteClause}
        />;
      case 'analysis':
        return <AnalyzePage user={user} setView={setView} />;
      case 'lifecycle':
        return <LifecyclePage
          user={user}
          setView={setView}
          onViewDocument={onViewDocument}
          onUpdateDocumentStatus={onUpdateDocumentStatus}
          allUsers={allUsers}
          onRequestSignatures={(docId, signers) => {
            onRequestSignatures(docId, signers);
          }}
        />;
      case 'obligations':
        return <ObligationsPage
          user={user}
          setView={setView}
          onViewDocument={onViewDocument}
          onUpdateObligationStatus={onUpdateObligationStatus}
        />;
      case 'workflows':
        return <WorkflowBuilderPage
          user={user}
          team={team}
          setView={setView}
          onCreateTemplate={onCreateWorkflowTemplate}
          onUpdateTemplate={onUpdateWorkflowTemplate}
          onDeleteTemplate={onDeleteWorkflowTemplate}
          workflowInstances={workflowInstances}
        />;
      case 'integrations':
        return <IntegrationsPage
          user={user}
          onCreateConnection={onCreateConnection}
          onDeleteConnection={onDeleteConnection}
          onCreateFlow={onCreateFlow}
          onUpdateFlow={onUpdateFlow}
          onDeleteFlow={onDeleteFlow}
        />;
      case 'subscription':
        return <SubscriptionPage user={user} onChangePlan={(plan) => onUpdateSubscription('', plan)} pricingPlans={pricingPlans} />;
      case 'team':
        return <TeamManagementPage
          user={user}
          team={team}
          allUsers={allUsers}
          setView={setView}
          onInviteMember={onInviteMember}
          onUpdateMemberRole={onUpdateMemberRole}
          onRemoveMember={onRemoveMember}
        />;
      case 'settings':
        return <SettingsPage
          user={user}
          team={team}
          onUpdateProfile={onUpdateProfile}
          onDeleteAccount={onDeleteAccount}
          onUpdateUserSettings={onUpdateUserSettings}
          onChangePassword={onChangePassword}
          onGenerateApiKey={onGenerateApiKey}
          onRevokeApiKey={onRevokeApiKey}
          onUpdateSsoConfig={onUpdateSsoConfig}
        />;
      case 'documentDetail':
        if (activeDocument) {
          return <DocumentDetailPage
            document={activeDocument}
            onBack={() => setView('history')}
            onSave={onUpdateDocument}
            onRevert={onRevertDocumentVersion}
            user={user}
            allUsers={allUsers}
            allTeams={allTeams}
            setView={setView}
            onUpdateCollaborators={onUpdateCollaborators}
            onAddComment={onAddComment}
            onAddReply={onAddReply}
            onResolveThread={onResolveThread}
            onDeleteComment={onDeleteComment}
            initialUtilityTab={initialUtilityTab}
            onClearInitialTab={onClearInitialTab}
            onCreateCustomTemplate={onCreateCustomTemplate}
            onRequestSignatures={onRequestSignatures}
            onCreateClause={onCreateClause}
            onUpdateDocumentClient={onUpdateDocumentClient}
            onRequestApproval={onRequestApproval}
            onRespondToApproval={onRespondToApproval}
            onUpdateObligationStatus={onUpdateObligationStatus}
            workflowInstances={workflowInstances}
          />;
        }
        // Fallback if no active document
        setView('history');
        return null;
      case 'notifications':
        return <NotificationsPage
          notifications={user.notifications || []}
          onNotificationClick={onNotificationClick}
          onMarkAllNotificationsRead={onMarkAllNotificationsRead}
        />;
      case 'help':
        return <HelpCenterPage initialTopicId={initialHelpTopicId} />;
      default:
        return <DashboardHome user={user} setView={setView} onViewDocument={onViewDocument} />;
    }
  }

  // Show error state if there's a data loading error
  if (dataError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">Error Loading Data</h3>
          <p className="text-gray-600">{dataError}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Loading is now handled by the skeleton layout in App.tsx to prevent double loading spinners */}
      {renderViewContent()}
    </>
  );
};

export default DashboardPage;
